import re
import os
from config import logger

# 全局缓存
keywords = []
downloaded_videos = set()

def sanitize_filename(filename):
    """清理文件名，去除非法字符，但保留空格"""
    filename = re.sub(r'[\/:*?"<>|#]', '', filename)  # 删除非法字符，保留空格
    return filename.strip()

def get_keywords():
    """获取当前加载的关键词列表"""
    global keywords
    return keywords

def load_keywords():
    """加载需要过滤的关键字"""
    global keywords
    from config import KEYWORDS_FILE
    
    if os.path.exists(KEYWORDS_FILE):
        with open(KEYWORDS_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
            keywords = [line.strip().lower() for line in content.splitlines() if line.strip()]
        # 只保留一条简洁的日志
        if keywords:
            logger.warning(f"已加载 {len(keywords)} 个关键字用于过滤")
    else:
        logger.error(f"未找到关键字文件: {KEYWORDS_FILE}")
        # 创建一个默认的关键词文件以便测试
        try:
            os.makedirs(os.path.dirname(KEYWORDS_FILE), exist_ok=True)
            with open(KEYWORDS_FILE, 'w', encoding='utf-8') as f:
                f.write("cancer\ntarot\nreading\n")
            # 重新加载
            with open(KEYWORDS_FILE, 'r', encoding='utf-8') as f:
                keywords = [line.strip().lower() for line in f if line.strip()]
            logger.warning(f"已创建并加载默认关键词")
        except Exception as e:
            logger.error(f"创建默认关键词文件失败: {e}")
    
    # 返回加载的关键词
    return keywords

def load_downloaded_history():
    """加载已下载的视频历史记录"""
    global downloaded_videos
    from config import HISTORY_VIDEOS_FILE
    
    if os.path.exists(HISTORY_VIDEOS_FILE):
        with open(HISTORY_VIDEOS_FILE, 'r', encoding='utf-8') as f:
            downloaded_videos = {line.split(' - ')[0].strip() for line in f}
        logger.warning(f"加载了 {len(downloaded_videos)} 个已下载的视频记录。")
    else:
        logger.error(f"未找到历史记录文件: {HISTORY_VIDEOS_FILE}")
        # 创建一个空的历史记录文件
        try:
            os.makedirs(os.path.dirname(HISTORY_VIDEOS_FILE), exist_ok=True)
            with open(HISTORY_VIDEOS_FILE, 'w', encoding='utf-8') as f:
                pass
            logger.warning(f"创建了空的历史记录文件: {HISTORY_VIDEOS_FILE}")
        except Exception as e:
            logger.error(f"创建历史记录文件失败: {e}")

def save_downloaded_history(video_id, channel_name, video_title, subtitle_path=None, thumbnail_path=None, timestamp_info=None):
    """保存已下载视频的记录，包含更多信息"""
    from config import HISTORY_VIDEOS_FILE
    
    # 基本记录
    record = f"{video_id} - [{channel_name}] {video_title}"
    
    # 添加额外信息 (无字幕版本：subtitle_path总是None)
    # if subtitle_path:
    #     record += f" | 字幕: {os.path.basename(subtitle_path)}"
    if thumbnail_path:
        record += f" | 封面: {os.path.basename(thumbnail_path)}"
    if timestamp_info and 'duration_formatted' in timestamp_info:
        record += f" | 时长: {timestamp_info['duration_formatted']}"
    
    with open(HISTORY_VIDEOS_FILE, 'a', encoding='utf-8') as f:
        f.write(record + "\n")
    downloaded_videos.add(video_id)
    logger.warning(f"记录已保存: {record}")

def is_video_in_history(video_id):
    """检查视频是否已经下载过"""
    return video_id in downloaded_videos 

def save_timestamp_info(video_id, channel_name, video_title, timestamp_info):
    """保存视频的时间戳信息到txt文件"""
    timestamp_dir = "D:/ytb_python_download/timestamps"
    os.makedirs(timestamp_dir, exist_ok=True)
    
    # 创建文件名
    filename = f"【{channel_name}】{video_title[:30]}_{video_id}.timestamp.txt"
    filepath = os.path.join(timestamp_dir, filename)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(f"视频ID: {video_id}\n")
        f.write(f"频道名称: {channel_name}\n")
        f.write(f"视频标题: {video_title}\n")
        f.write(f"时长: {timestamp_info.get('duration_formatted', '未知')}\n")
        f.write(f"上传日期: {timestamp_info.get('upload_date', '未知')}\n")
        f.write(f"观看次数: {timestamp_info.get('view_count', '未知')}\n")
    
    logger.warning(f"时间戳信息已保存到: {filepath}")
    return filepath 
