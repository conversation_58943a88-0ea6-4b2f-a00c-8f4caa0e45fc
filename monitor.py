import os
import time
from datetime import timedelta
from config import logger, CHANNELS_VIDEOS_FILE, DOWNLOAD_PATHS, CHECK_INTERVAL
from utils import load_downloaded_history, load_keywords, get_keywords
from video_info import get_latest_videos_with_retry
from downloader import download_video
import subprocess

def get_video_duration(file_path):
    """获取视频时长（秒）"""
    try:
        # 使用ffprobe获取视频时长
        cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{file_path}"'
        output = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
        return float(output) if output else None
    except Exception as e:
        logger.error(f"获取视频时长出错: {e}")
        return None

def process_channel(channel_name, channel_id, time_range, download_location, extra_info):
    """
    处理单个频道的视频下载逻辑（无字幕版本）。
    """
    logger.warning(f"检查频道 {channel_name} 的新视频... ({extra_info})")
    try:
        videos = get_latest_videos_with_retry(channel_id, time_range)
    except Exception as e:
        logger.error(f"获取频道视频列表失败: {e}")
        return

    # 获取当前关键词列表（不输出日志）
    filter_keywords = get_keywords()
    
    for video_id, video_title, upload_date in videos:
        # 创建一个更简洁的过滤条件检查
        should_filter = False
        if channel_name != "222":
            for keyword in filter_keywords:
                if keyword.lower() in video_title.lower():
                    should_filter = True
                    break
            
            if should_filter:
                # 简化日志输出，仅在跳过时简单记录
                logger.warning(f"跳过视频: {video_title}")
                continue
        
        logger.warning(f"发现新视频: {video_title} ({video_id})")
        logger.warning(f"上传日期: {upload_date}")
        
        # 下载视频（无字幕版本）
        success = download_video(video_id, video_title, channel_name, download_location, upload_date)
        
        # 如果下载成功，检查视频时长
        if success:
            # 构建文件路径
            from translation import translate_title_with_retry
            from utils import sanitize_filename
            
            translated_title = translate_title_with_retry(video_title)
            sanitized_title_short = translated_title[:30]
            filename = f'【{channel_name}】{sanitized_title_short}_{upload_date.strftime("%Y-%m-%d")}.mp4'
            file_path = os.path.join(download_location, filename)
            
            if os.path.exists(file_path):
                # 获取视频时长
                duration = get_video_duration(file_path)
                
                # 如果是短视频（≤3分钟），修改文件名添加"_short"
                if duration is not None and duration <= 180:  # 180秒 = 3分钟
                    logger.warning(f"检测到短视频：{file_path}，时长：{duration}秒")
                    
                    # 生成新文件名
                    file_dir = os.path.dirname(file_path)
                    file_name = os.path.basename(file_path)
                    name_parts = os.path.splitext(file_name)
                    new_name = f"{name_parts[0]}_short{name_parts[1]}"
                    new_path = os.path.join(file_dir, new_name)
                    
                    # 重命名文件
                    try:
                        os.rename(file_path, new_path)
                        logger.warning(f"已将短视频重命名为：{new_name}")
                    except Exception as e:
                        logger.error(f"重命名短视频失败：{e}")
                else:
                    logger.warning(f"普通视频，时长：{duration}秒")

def load_channels():
    """
    从配置文件加载频道列表
    """
    channels = []
    if os.path.exists(CHANNELS_VIDEOS_FILE):
        with open(CHANNELS_VIDEOS_FILE, 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 4:  # 至少有四个字段
                    channel_name = parts[0]
                    channel_id = parts[1]
                    try:
                        time_range = int(parts[2])  # 时间范围，转换为整数
                    except ValueError:
                        logger.error(f"无效的时间范围 '{parts[2]}'，默认设置为24小时。")
                        time_range = 24  # 默认24小时
                    download_type = parts[3].lower()  # "required" 或 "alternative"
                    
                    # 映射到实际的下载路径
                    download_location = DOWNLOAD_PATHS.get(download_type)
                    if not download_location:
                        logger.error(f"未知的下载类型 '{download_type}'，跳过该频道。")
                        continue
                    
                    extra_info = " ".join(parts[4:]) if len(parts) > 4 else ""
                    channels.append((channel_name, channel_id, timedelta(hours=time_range), download_location, extra_info))
    else:
        logger.error(f"未找到频道文件: {CHANNELS_VIDEOS_FILE}")
    
    return channels

def monitor_video_channels():
    """
    监控视频频道，下载新视频（无字幕版本）
    """
    load_downloaded_history()
    load_keywords()  # 不需要保存返回值
    
    channels = load_channels()
    
    if not channels:
        logger.warning("没有频道可供监控，请检查 channels_videos_test.txt 文件。")
        return

    for channel_name, channel_id, time_range, download_location, extra_info in channels:
        process_channel(channel_name, channel_id, time_range, download_location, extra_info)

def start_video_monitoring():
    """
    启动视频监控（无字幕版本）
    """
    while True:
        try:
            logger.warning("开始检查视频...（无字幕版本）")
            monitor_video_channels()
        except Exception as e:
            logger.error(f"监控过程中发生错误：{e}，等待 {CHECK_INTERVAL} 秒后重试...")
        finally:
            minutes = CHECK_INTERVAL // 60
            seconds = CHECK_INTERVAL % 60
            logger.warning(f"检查完成，等待 {minutes} 分钟 {seconds} 秒后再次检查。")
            time.sleep(CHECK_INTERVAL) 
