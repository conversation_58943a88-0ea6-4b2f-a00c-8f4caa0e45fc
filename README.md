# YouTube 视频下载器 - 无字幕版本

这是YouTube视频下载器的简化版本，**不包含字幕下载功能**。

## 主要特点

- ✅ 自动监控YouTube频道
- ✅ 下载视频文件
- ✅ 下载缩略图
- ✅ 提取时间戳信息
- ✅ 标题翻译
- ✅ 关键词过滤
- ✅ 短视频检测和标记
- ❌ **不下载字幕**（已注释掉相关功能）

## 与原版的区别

1. **移除了字幕下载功能**：
   - 注释掉了 `subtitle.py` 的导入
   - 注释掉了字幕验证函数
   - 跳过字幕下载步骤

2. **简化了下载流程**：
   - 不再检查字幕文件
   - 不再因为字幕下载失败而跳过视频下载

3. **保留了所有其他功能**：
   - 视频下载
   - 缩略图下载
   - 时间戳提取
   - 翻译功能

## 使用方法

```bash
cd no_subtitle_version
python main.py
```

## 配置文件

- `channels_videos_test.txt` - 频道配置
- `keywords.txt` - 过滤关键词
- `downloaded_videos.txt` - 下载历史记录

## 注意事项

这个版本是为了在字幕下载功能出现问题时的临时解决方案。当原版的字幕下载问题解决后，建议使用原版。

## 文件结构

```
no_subtitle_version/
├── main.py                    # 主程序
├── config.py                  # 配置文件
├── downloader.py              # 视频下载（已注释字幕部分）
├── monitor.py                 # 监控功能
├── video_info.py              # 视频信息获取
├── utils.py                   # 工具函数
├── translation.py             # 翻译功能
├── thumbnail_downloader.py    # 缩略图下载
├── channels_videos_test.txt   # 频道配置
├── keywords.txt               # 关键词过滤
├── downloaded_videos.txt      # 下载历史
└── README.md                  # 说明文档
```
