import os
import logging
import time

# 获取当前脚本所在的绝对路径
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

# 配置日志
logging.basicConfig(
    level=logging.WARNING,               # 设置日志级别为 WARNING，显示 WARNING 及以上级别的日志
    format='%(message)s',                # 简化日志格式，仅显示消息内容
    handlers=[
        logging.FileHandler(os.path.join(CURRENT_DIR, "script.log"), encoding='utf-8'),  # 写入日志文件
        logging.StreamHandler()                                 # 同时输出到控制台
    ]
)

logger = logging.getLogger(__name__)

# 文件路径配置
CHANNELS_VIDEOS_FILE = os.path.join(CURRENT_DIR, 'channels_videos_test.txt')
HISTORY_VIDEOS_FILE = os.path.join(CURRENT_DIR, 'downloaded_videos.txt')
KEYWORDS_FILE = os.path.join(CURRENT_DIR, 'keywords.txt')

# 时间配置
CHECK_INTERVAL = 600  # 检查间隔时间（秒）- 增加到10分钟减少请求频率
YTDLP_COMMAND_INTERVAL = 30  # yt-dlp命令之间的间隔时间（秒）

# 代理配置
USE_PROXY = True         # 设置为 True 如果需要使用代理
PROXY_URL = "http://127.0.0.1:7897"  # 使用混合代理端口

# 字幕目录
VTT_HAN_FOLDER = "D:/ytb_python_download/vtt_han"
SRT_HAN_FOLDER = "D:/ytb_python_download/srt_han"
os.makedirs(VTT_HAN_FOLDER, exist_ok=True)
os.makedirs(SRT_HAN_FOLDER, exist_ok=True)

# 下载路径配置
DOWNLOAD_PATHS = {
    "required": "D:/ytb_python_download/",
    "alternative": "D:/ytb_python_download/alternative"
}

# 翻译API配置
TRANSLATE_API_URL = "https://translate.jayogo.com/v1/chat/completions"
TRANSLATE_API_KEY = "Bearer sk-mJtHjWKwIU10u3kiEfA3D22838A847079cBb66B88e2187A6"

# yt-dlp命令执行管理器
class YtDlpCommandManager:
    """管理yt-dlp命令的执行间隔，避免请求过于频繁"""
    _last_command_time = 0
    
    @classmethod
    def wait_before_command(cls):
        """在执行yt-dlp命令前等待适当的时间间隔"""
        current_time = time.time()
        time_since_last = current_time - cls._last_command_time
        
        if time_since_last < YTDLP_COMMAND_INTERVAL:
            wait_time = YTDLP_COMMAND_INTERVAL - time_since_last
            logger.warning(f"等待 {wait_time:.1f} 秒后执行下一个yt-dlp命令...")
            time.sleep(wait_time)
        
        cls._last_command_time = time.time()

# 创建全局实例
ytdlp_manager = YtDlpCommandManager()
